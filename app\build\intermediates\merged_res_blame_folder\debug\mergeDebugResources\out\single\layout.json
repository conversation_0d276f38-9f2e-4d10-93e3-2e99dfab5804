[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-mergeDebugResources-42:\\layout\\fragment_second.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-45:\\layout\\fragment_second.xml"}, {"merged": "com.tugas.platform.modulcoordinatorlayout.app-mergeDebugResources-42:/layout/fragment_second.xml", "source": "com.tugas.platform.modulcoordinatorlayout.app-main-45:/layout/fragment_second.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-mergeDebugResources-42:\\layout\\fragment_first.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-45:\\layout\\fragment_first.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-mergeDebugResources-42:\\layout\\content_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-45:\\layout\\content_main.xml"}, {"merged": "com.tugas.platform.modulcoordinatorlayout.app-mergeDebugResources-42:/layout/fragment_first.xml", "source": "com.tugas.platform.modulcoordinatorlayout.app-main-45:/layout/fragment_first.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-mergeDebugResources-42:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-45:\\layout\\activity_main.xml"}]