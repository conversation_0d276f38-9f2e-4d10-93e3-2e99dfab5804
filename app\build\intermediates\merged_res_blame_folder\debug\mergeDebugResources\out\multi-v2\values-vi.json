{"logs": [{"outputFile": "com.tugas.platform.modulcoordinatorlayout.app-mergeDebugResources-41:/values-vi/values-vi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7f8533f25255a319f35b30aaa85a62c6\\transformed\\navigation-ui-2.8.9\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,163", "endColumns": "107,112", "endOffsets": "158,271"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "9337,9445", "endColumns": "107,112", "endOffsets": "9440,9553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\6683f027ccec9929fc1d92af97d6b5b4\\transformed\\core-1.13.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "38,39,40,41,42,43,44,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3402,3499,3601,3700,3800,3903,4016,9956", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "3494,3596,3695,3795,3898,4011,4127,10052"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f4e5046d748962d6b3ddedbf703a4c5\\transformed\\appcompat-1.7.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,314,423,507,610,729,807,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1904,2008,2116,2217,2322,2437,2542,2699,2798", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "207,309,418,502,605,724,802,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1899,2003,2111,2212,2317,2432,2537,2694,2793,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,422,524,633,717,820,939,1017,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2114,2218,2326,2427,2532,2647,2752,2909,9636", "endColumns": "106,101,108,83,102,118,77,75,90,92,94,93,99,92,94,93,90,90,83,103,107,100,104,114,104,156,98,84", "endOffsets": "417,519,628,712,815,934,1012,1088,1179,1272,1367,1461,1561,1654,1749,1843,1934,2025,2109,2213,2321,2422,2527,2642,2747,2904,3003,9716"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\029d29095bf3280d16ab3d0259cf3316\\transformed\\material-1.12.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,344,421,499,579,659,758,872,952,1015,1078,1172,1246,1305,1391,1453,1514,1572,1636,1697,1751,1868,1925,1985,2039,2114,2241,2325,2405,2500,2584,2662,2792,2876,2954,3088,3179,3260,3311,3362,3428,3496,3572,3643,3723,3802,3877,3950,4026,4132,4221,4298,4389,4483,4557,4627,4720,4769,4850,4916,5001,5087,5149,5213,5276,5347,5446,5551,5649,5754,5809,5864,5942,6024,6103", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,76,77,79,79,98,113,79,62,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,79,94,83,77,129,83,77,133,90,80,50,50,65,67,75,70,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77,81,78,73", "endOffsets": "260,339,416,494,574,654,753,867,947,1010,1073,1167,1241,1300,1386,1448,1509,1567,1631,1692,1746,1863,1920,1980,2034,2109,2236,2320,2400,2495,2579,2657,2787,2871,2949,3083,3174,3255,3306,3357,3423,3491,3567,3638,3718,3797,3872,3945,4021,4127,4216,4293,4384,4478,4552,4622,4715,4764,4845,4911,4996,5082,5144,5208,5271,5342,5441,5546,5644,5749,5804,5859,5937,6019,6098,6172"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3008,3087,3164,3242,3322,4132,4231,4345,4425,4488,4551,4645,4719,4778,4864,4926,4987,5045,5109,5170,5224,5341,5398,5458,5512,5587,5714,5798,5878,5973,6057,6135,6265,6349,6427,6561,6652,6733,6784,6835,6901,6969,7045,7116,7196,7275,7350,7423,7499,7605,7694,7771,7862,7956,8030,8100,8193,8242,8323,8389,8474,8560,8622,8686,8749,8820,8919,9024,9122,9227,9282,9558,9721,9803,9882", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "endColumns": "12,78,76,77,79,79,98,113,79,62,62,93,73,58,85,61,60,57,63,60,53,116,56,59,53,74,126,83,79,94,83,77,129,83,77,133,90,80,50,50,65,67,75,70,79,78,74,72,75,105,88,76,90,93,73,69,92,48,80,65,84,85,61,63,62,70,98,104,97,104,54,54,77,81,78,73", "endOffsets": "310,3082,3159,3237,3317,3397,4226,4340,4420,4483,4546,4640,4714,4773,4859,4921,4982,5040,5104,5165,5219,5336,5393,5453,5507,5582,5709,5793,5873,5968,6052,6130,6260,6344,6422,6556,6647,6728,6779,6830,6896,6964,7040,7111,7191,7270,7345,7418,7494,7600,7689,7766,7857,7951,8025,8095,8188,8237,8318,8384,8469,8555,8617,8681,8744,8815,8914,9019,9117,9222,9277,9332,9631,9798,9877,9951"}}]}]}