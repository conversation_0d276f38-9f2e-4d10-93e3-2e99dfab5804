{"logs": [{"outputFile": "com.tugas.platform.modulcoordinatorlayout.app-mergeReleaseResources-41:/values-v23/values-v23.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f4e5046d748962d6b3ddedbf703a4c5\\transformed\\appcompat-1.7.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,20,34,35,36,39,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1225,1975,2094,2221,2443,2667,2782,2889,3002", "endLines": "2,3,4,5,19,33,34,35,38,42,43,44,45,49", "endColumns": "134,134,74,86,12,12,118,126,12,12,114,106,112,12", "endOffsets": "185,320,395,482,1220,1970,2089,2216,2438,2662,2777,2884,2997,3227"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\029d29095bf3280d16ab3d0259cf3316\\transformed\\material-1.12.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,5,9,13,16,19,22,25,28,32", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,271,413,574,784,991,1198,1401,1603,1868", "endLines": "4,8,12,15,18,21,24,27,31,35", "endColumns": "10,10,10,10,10,10,10,10,10,10", "endOffsets": "266,408,569,779,986,1193,1396,1598,1863,2136"}, "to": {"startLines": "59,62,66,70,73,76,79,82,85,89", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3731,3947,4089,4250,4460,4667,4874,5077,5279,5544", "endLines": "61,65,69,72,75,78,81,84,88,92", "endColumns": "10,10,10,10,10,10,10,10,10,10", "endOffsets": "3942,4084,4245,4455,4662,4869,5072,5274,5539,5812"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\475251a6ebd2845d6693bafdef0fbd00\\transformed\\cardview-1.0.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "4", "endColumns": "12", "endOffsets": "200"}, "to": {"startLines": "50", "startColumns": "4", "startOffsets": "3232", "endLines": "52", "endColumns": "12", "endOffsets": "3377"}}, {"source": "D:\\Documents\\3School Bullshit\\PBP\\ModulCoordinatorLayout\\app\\src\\main\\res\\values-v23\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "64", "endLines": "7", "endColumns": "12", "endOffsets": "458"}, "to": {"startLines": "53", "startColumns": "4", "startOffsets": "3382", "endLines": "58", "endColumns": "12", "endOffsets": "3726"}}]}]}