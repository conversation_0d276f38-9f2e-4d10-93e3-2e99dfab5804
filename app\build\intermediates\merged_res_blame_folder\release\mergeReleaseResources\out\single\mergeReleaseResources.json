[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\drawable_fubuki_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\drawable\\fubuki_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\navigation_nav_graph.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\navigation\\nav_graph.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\xml\\backup_rules.xml"}, {"merged": "com.tugas.platform.modulcoordinatorlayout.app-release-43:/drawable_ic_fubuki.xml.flat", "source": "com.tugas.platform.modulcoordinatorlayout.app-main-44:/drawable/ic_fubuki.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\layout_fragment_second.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\layout\\fragment_second.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\drawable_header_image.jpg.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\drawable\\header_image.jpg"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\layout_fragment_first.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\layout\\fragment_first.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\drawable_ic_fubuki.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\drawable\\ic_fubuki.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\layout_content_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\layout\\content_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\drawable_ic_review.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\drawable\\ic_review.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-release-43:\\menu_menu_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.tugas.platform.modulcoordinatorlayout.app-main-44:\\menu\\menu_main.xml"}]