<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res"><file name="ic_launcher" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_background" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_review" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\drawable\ic_review.xml" qualifiers="" type="drawable"/><file name="header_image" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\drawable\header_image.jpg" qualifiers="" type="drawable"/><file name="ic_fubuki" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\drawable\ic_fubuki.xml" qualifiers="" type="drawable"/><file name="fubuki_header" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\drawable\fubuki_header.xml" qualifiers="" type="drawable"/><file path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Shirakami Fubuki</string><string name="review_text">Shirakami Fubuki adalah seorang Virtual YouTuber dari Hololive yang sangat populer. Dia dikenal dengan kepribadiannya yang ceria dan suka bermain game...</string><string name="next">Selanjutnya</string><string name="previous">Sebelumnya</string><string name="first_fragment_label">Profil Fubuki</string><string name="second_fragment_label">Informasi Tambahan</string><string name="action_settings">Pengaturan</string><string name="lorem_ipsum">Shirakami Fubuki adalah anggota Hololive Gamers yang debut pada tanggal 13 Mei 2018. Dia adalah rubah putih yang sangat energik dan suka bermain berbagai jenis game. Fubuki-chan dikenal dengan tawanya yang khas dan interaksinya yang hangat dengan para penonton.</string><string name="fubuki_title">Profil Shirakami Fubuki</string><string name="fubuki_debut_date">Debut: 13 Mei 2018</string><string name="fubuki_description">Shirakami Fubuki adalah seorang rubah putih yang bekerja sebagai Virtual YouTuber di bawah naungan Hololive Production. Dia adalah anggota dari Hololive Gamers dan dikenal dengan kepribadiannya yang ceria, energik, dan suka bermain game. Fubuki sering berinteraksi dengan penonton dan dikenal dengan catchphrase-nya yang unik.</string><string name="fubuki_characteristics">Karakteristik: Rubah putih yang energik, suka bermain game, memiliki tawa yang khas, dan sangat ramah kepada penonton. Dia juga dikenal sebagai "Friend" oleh para penggemar. Fubuki-chan sering bermain game horror, FPS, dan berbagai game lainnya dengan reaksi yang sangat menghibur.</string><string name="fubuki_additional_info">Informasi Tambahan:\n\n• Nama: Shirakami Fubuki (白上フブキ)\n• Spesies: Rubah Putih\n• Ulang Tahun: 5 Oktober\n• Tinggi: 155 cm\n• Hobi: Bermain game, menonton anime\n• Makanan Favorit: Hamburger\n• Catchphrase: "Kon kon kitsune~"\n\nFubuki adalah salah satu VTuber paling populer di Hololive dan sering disebut sebagai "Teman" oleh para penggemarnya karena sifatnya yang ramah dan mudah bergaul.</string></file><file path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.ModulCoordinatorLayout" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.ModulCoordinatorLayout" parent="Base.Theme.ModulCoordinatorLayout"/></file><file path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="green">#96ff84</color></file><file path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="fab_margin">16dp</dimen></file><file path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.ModulCoordinatorLayout" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values-v23\themes.xml" qualifiers="v23"><style name="Theme.ModulCoordinatorLayout" parent="Base.Theme.ModulCoordinatorLayout">
        
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">?attr/isLightTheme</item>
    </style></file><file name="activity_main" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="content_main" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\layout\content_main.xml" qualifiers="" type="layout"/><file name="fragment_first" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\layout\fragment_first.xml" qualifiers="" type="layout"/><file name="fragment_second" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\layout\fragment_second.xml" qualifiers="" type="layout"/><file path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values-land\dimens.xml" qualifiers="land"><dimen name="fab_margin">48dp</dimen></file><file path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values-w600dp\dimens.xml" qualifiers="w600dp-v13"><dimen name="fab_margin">48dp</dimen></file><file path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values-w1240dp\dimens.xml" qualifiers="w1240dp-v13"><dimen name="fab_margin">200dp</dimen></file><file name="menu_main" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\menu\menu_main.xml" qualifiers="" type="menu"/><file name="nav_graph" path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\navigation\nav_graph.xml" qualifiers="" type="navigation"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>