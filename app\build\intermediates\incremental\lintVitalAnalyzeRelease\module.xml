<lint-module
    format="1"
    dir="D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app"
    name=":app"
    type="APP"
    maven="Modul Coordinator Layout:app:unspecified"
    agpVersion="8.9.1"
    buildFolder="build"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\Sdk\platforms\android-35\android.jar;C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\35.0.0\core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
