<?xml version="1.0" encoding="utf-8"?>
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="400dp"
    android:height="200dp"
    android:viewportWidth="400"
    android:viewportHeight="200">
    
    <!-- Background gradient (sky blue to white) -->
    <path android:pathData="M0,0h400v200h-400z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="0"
                android:startY="0"
                android:endX="0"
                android:endY="200"
                android:type="linear">
                <item android:color="#87CEEB" android:offset="0.0" />
                <item android:color="#E0F6FF" android:offset="1.0" />
            </gradient>
        </aapt:attr>
    </path>
    
    <!-- Simple fox silhouette -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M200,60 C180,60 160,80 160,100 C160,120 180,140 200,140 C220,140 240,120 240,100 C240,80 220,60 200,60 Z" />
    
    <!-- Fox ears -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M180,70 L170,50 L190,60 Z" />
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M220,70 L210,60 L230,50 Z" />
    
    <!-- Simple text background -->
    <path
        android:fillColor="#40FFFFFF"
        android:pathData="M50,150 h300 v30 h-300 z" />
    
</vector>
