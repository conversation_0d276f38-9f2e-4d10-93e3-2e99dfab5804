1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.tugas.platform.modulcoordinatorlayout"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <permission
11-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6683f027ccec9929fc1d92af97d6b5b4\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.tugas.platform.modulcoordinatorlayout.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6683f027ccec9929fc1d92af97d6b5b4\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6683f027ccec9929fc1d92af97d6b5b4\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.tugas.platform.modulcoordinatorlayout.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6683f027ccec9929fc1d92af97d6b5b4\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6683f027ccec9929fc1d92af97d6b5b4\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:5:5-25:19
18        android:allowBackup="true"
18-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:6:9-35
19        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
19-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6683f027ccec9929fc1d92af97d6b5b4\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
20        android:dataExtractionRules="@xml/data_extraction_rules"
20-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:7:9-65
21        android:extractNativeLibs="false"
22        android:fullBackupContent="@xml/backup_rules"
22-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:8:9-54
23        android:icon="@mipmap/ic_launcher"
23-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:9:9-43
24        android:label="@string/app_name"
24-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:10:9-41
25        android:roundIcon="@mipmap/ic_launcher_round"
25-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:11:9-54
26        android:supportsRtl="true"
26-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:12:9-35
27        android:theme="@style/Theme.ModulCoordinatorLayout" >
27-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:13:9-60
28        <activity
28-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:15:9-24:20
29            android:name="com.tugas.platform.modulcoordinatorlayout.MainActivity"
29-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:16:13-41
30            android:exported="true"
30-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:17:13-36
31            android:theme="@style/Theme.ModulCoordinatorLayout" >
31-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:18:13-64
32            <intent-filter>
32-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:19:13-23:29
33                <action android:name="android.intent.action.MAIN" />
33-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:20:17-69
33-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:20:25-66
34
35                <category android:name="android.intent.category.LAUNCHER" />
35-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:22:17-77
35-->D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\AndroidManifest.xml:22:27-74
36            </intent-filter>
37        </activity>
38
39        <provider
39-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ebe80b1183f4436d5a74a22860afa8fb\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
40            android:name="androidx.startup.InitializationProvider"
40-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ebe80b1183f4436d5a74a22860afa8fb\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
41            android:authorities="com.tugas.platform.modulcoordinatorlayout.androidx-startup"
41-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ebe80b1183f4436d5a74a22860afa8fb\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
42            android:exported="false" >
42-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ebe80b1183f4436d5a74a22860afa8fb\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
43            <meta-data
43-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ebe80b1183f4436d5a74a22860afa8fb\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
44                android:name="androidx.emoji2.text.EmojiCompatInitializer"
44-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ebe80b1183f4436d5a74a22860afa8fb\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
45                android:value="androidx.startup" />
45-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ebe80b1183f4436d5a74a22860afa8fb\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
46            <meta-data
46-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fbecb04857d79054ae0261369c05274\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
47                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
47-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fbecb04857d79054ae0261369c05274\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
48                android:value="androidx.startup" />
48-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3fbecb04857d79054ae0261369c05274\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
49            <meta-data
49-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
50                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
50-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
51                android:value="androidx.startup" />
51-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
52        </provider>
53
54        <uses-library
54-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c29f5ba29ac053de0b7b4187bfdc16f\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
55            android:name="androidx.window.extensions"
55-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c29f5ba29ac053de0b7b4187bfdc16f\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
56            android:required="false" />
56-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c29f5ba29ac053de0b7b4187bfdc16f\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
57        <uses-library
57-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c29f5ba29ac053de0b7b4187bfdc16f\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
58            android:name="androidx.window.sidecar"
58-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c29f5ba29ac053de0b7b4187bfdc16f\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
59            android:required="false" />
59-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0c29f5ba29ac053de0b7b4187bfdc16f\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
60
61        <receiver
61-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
62            android:name="androidx.profileinstaller.ProfileInstallReceiver"
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
63            android:directBootAware="false"
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
64            android:enabled="true"
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
65            android:exported="true"
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
66            android:permission="android.permission.DUMP" >
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
67            <intent-filter>
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
68                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
69            </intent-filter>
70            <intent-filter>
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
71                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
72            </intent-filter>
73            <intent-filter>
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
74                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
75            </intent-filter>
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
77                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f98a8c060bc2ca5673d98dc964de591c\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
78            </intent-filter>
79        </receiver>
80    </application>
81
82</manifest>
