# List of SDK dependencies of this app, this information is also included in an encrypted form in the APK.
# For more information visit: https://d.android.com/r/tools/dependency-metadata

library {
  maven_library {
    groupId: "androidx.databinding"
    artifactId: "viewbinding"
    version: "8.9.1"
  }
  digests {
    sha256: "Y\330\207Nj\027\310\200\227\224\030\240\b\215\355\023\276X\351A\234\t\334\227\326\327\206O\236w\325\036"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation"
    version: "1.8.1"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-jvm"
    version: "1.8.1"
  }
  digests {
    sha256: "\232\2532m\224\222\200\t\221\205C`\254$\217I<\347\367\303\0305\0310\233x\254\351\342@\366\366"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib"
    version: "1.9.22"
  }
  digests {
    sha256: "j\276\024l\'\206A8\270t\314\314\376_SN>\271#\311\232\033{]EIN\345iO>\n"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains"
    artifactId: "annotations"
    version: "13.0"
  }
  digests {
    sha256: "\254\342\241\r\310\342\325\3754\222^\312\300>I\210\262\300\370Qe\f\224\270\316\364\233\241\275\021\024x"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-common"
    version: "1.9.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk8"
    version: "1.8.22"
  }
  digests {
    sha256: "A\230\260\352\360\220\244\362[o~ZYX\037C\024\272\214\237l\321\321>\351\323H\346^\330\367\a"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-stdlib-jdk7"
    version: "1.8.22"
  }
  digests {
    sha256: "\005_\\\262B\207\372\020a\000\231Z{G\253\222\022k\201\3502\350u\365\372,\360\275Ui=\v"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat"
    version: "1.7.0"
  }
  digests {
    sha256: "g\030\227\023\263\n?\253iqq<\305\372\261\313\177\002+\317d\212%ucq\\\221\327\031\325\204"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity"
    version: "1.8.0"
  }
  digests {
    sha256: "\323\246vp\235\352\004\362\250Pn*\350PR\377\367c\333Rj\307\361k\004\336P\375\320[\a "
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection"
    version: "1.4.2"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-jvm"
    version: "1.4.2"
  }
  digests {
    sha256: "\230L\351\275x\000U\352\373\212\020Z\312?QF\206\277{\033br\354|dZ\230\316@\374}\264"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.collection"
    artifactId: "collection-ktx"
    version: "1.4.2"
  }
  digests {
    sha256: "\306\336\255\242\372\305;\216\246R=\275\247u\227\261(\000ftao\024\017\004\337#&Lm\032\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core"
    version: "1.13.0"
  }
  digests {
    sha256: "\033\226\310\353\020\304\264\002\203\375\326\351\252t\377\377\005\372\344\361]T\366\033\246\235Q\177\315\024F\225"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.annotation"
    artifactId: "annotation-experimental"
    version: "1.4.1"
  }
  digests {
    sha256: "k\324\307\307Go\202`\315;\333\270\021\203X>\223\374\237y\f\'\336\247\3341A\201\313\370z\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.concurrent"
    artifactId: "concurrent-futures"
    version: "1.1.0"
  }
  digests {
    sha256: "\f\340g\305\024\240\321\004\235\033\353\337p\2364N\323&o\351tBuh)7\315\313\0233N\236"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.guava"
    artifactId: "listenablefuture"
    version: "1.0"
  }
  digests {
    sha256: "\344\255v\a\345\300G|o\211\016\362jI\313\215\033\264\337\373e\v\253E\002\257\356ddN0i"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.interpolator"
    artifactId: "interpolator"
    version: "1.0.0"
  }
  digests {
    sha256: "3\03115\246O\342\037\242\303^\354f\210\361\247nQ&\006\300\374\203\334\033h\2367\255\327s*"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime"
    version: "2.6.2"
  }
  digests {
    sha256: "Hg\375Ryt/\272\203\210\202\0310\313*\377\340m\201\245(\024\347\344\036p9.\240\357\210|"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-common"
    version: "2.2.0"
  }
  digests {
    sha256: "e0\212\006\261\300\016\341\206\313\236\0312\023\203\360C\271\223\201?\025\"\304\177J>3\003\275\272A"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.arch.core"
    artifactId: "core-runtime"
    version: "2.2.0"
  }
  digests {
    sha256: "\241\276^\f\252+\ab8b\257j\342\033:\260q\201#$Q\204\320\343\r\352\201\265?\231\nG"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-common"
    version: "2.6.2"
  }
  digests {
    sha256: "\363H1\266\307\034\330D\341\323]\033\344\235^yD|Z\270V4e1\261\350go\332st\261"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-android"
    version: "1.6.4"
  }
  digests {
    sha256: "?\334\016\355[\304\270>\351b\'tR\n-\262Tp7\016\254\321X\034\254\0367pO\t[\000"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core"
    version: "1.6.4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-core-jvm"
    version: "1.6.4"
  }
  digests {
    sha256: "\302L\213\262{\263 \304\2518qP\032~^\fa`v8\220{\031z\357gU\023\324\310 \276"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-coroutines-bom"
    version: "1.6.4"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core"
    version: "2.6.2"
  }
  digests {
    sha256: "\"Vx\n<\377J\036W\373\263\324BU|\027\3346:\270\257\020[\312\365&\035\216-]\271I"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata"
    version: "2.6.2"
  }
  digests {
    sha256: "g5\237`\235\374+\366]\241\'\v#\003?\205`d\354\'\237\005\216\np\307\025\367\311\00001"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-livedata-core-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "\237,\026\343:U\272\215g\243b:U\276\377\362\354\200d>?\n\222g`\337\035\225|?\212\357"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-process"
    version: "2.6.2"
  }
  digests {
    sha256: "\0173\261\275\001\177\226Zj\373.{\363\343\2754<b@a\301\230m\352\213\242F\235\0044\2247"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.startup"
    artifactId: "startup-runtime"
    version: "1.1.1"
  }
  digests {
    sha256: "\340\2462\2327\022b\376LE\003r\267\017\332\363;v\236\366\221p\224r7\207\317\316\211k\035\323"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.tracing"
    artifactId: "tracing"
    version: "1.0.0"
  }
  digests {
    sha256: "\a\270\266\023\226e\270\204\241b\354\317\227\211\034\245\017\177V\203\0223\277%\026\212\340O{V\206\022"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-runtime-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "(\240\2704\364\016\335R\342\370\001\v\3340W\322\240\314v\252\245\254\223\021\255\260\271\316\221\234\251\314"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel"
    version: "2.6.2"
  }
  digests {
    sha256: "\344\377C8\231\236\034l\234rG\031\365\324\252}\326\033\366\365E\325%j\'\251\323u\337\237#0"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-ktx"
    version: "2.6.2"
  }
  digests {
    sha256: "\177\300\372#J3!\261\363M\265\206+\027\332\203\321\306,\033\306n\302\375O\033\260\247q\254\372\273"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.lifecycle"
    artifactId: "lifecycle-viewmodel-savedstate"
    version: "2.6.2"
  }
  digests {
    sha256: "{\307\334\272\261v6\354\ao\022\257\344\320&q&\\8\224W\261\263f\263z\016\214\271\036-\240"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.core"
    artifactId: "core-ktx"
    version: "1.13.0"
  }
  digests {
    sha256: "Y\305Na)\351&\034m\201\017\216\352Jn\342\364\231M\201b\261\315\237\310\214\234\204\311*\314\374"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate"
    version: "1.2.1"
  }
  digests {
    sha256: "!\247\324\274\366\275\271J\327\271(8\001R\223\000\264\373\270\200\214\244\361\221\340\315\316o\330\344pZ"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.savedstate"
    artifactId: "savedstate-ktx"
    version: "1.2.1"
  }
  digests {
    sha256: "\205S\370~q6\302N\305$5`\364\217\0342\313\245m\252wr/\211X\232\\\257\313\217x\224"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.profileinstaller"
    artifactId: "profileinstaller"
    version: "1.4.0"
  }
  digests {
    sha256: "\325\002\024\037\314\351\002C\017b\266t\303*\354\320\367Rb\347\356,\321\\t\255\266\027\315\023\023\n"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.versionedparcelable"
    artifactId: "versionedparcelable"
    version: "1.1.1"
  }
  digests {
    sha256: "W\350\3312`\321\215[\220\a\311\356\323\306J\321Y\336\220\310`\236\277\307J4|\275QE5\244"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.activity"
    artifactId: "activity-ktx"
    version: "1.8.0"
  }
  digests {
    sha256: "\277\356\022\301\310\214?t\225O\277ngf\274\0309V\363tx\267\300$\372\347\365\263\204\223\327\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.appcompat"
    artifactId: "appcompat-resources"
    version: "1.7.0"
  }
  digests {
    sha256: "U\266w\206\002h\017<(\214\343P\242\302\323\335\025\215\227\333\377\3064v\'X&eU\202\303\210"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable"
    version: "1.1.0"
  }
  digests {
    sha256: "F\375c:\300\033I\267\374\253\302c\277\t\214Z\213\236\232iwM#N\334\312\004\373\002\337\216&"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.vectordrawable"
    artifactId: "vectordrawable-animated"
    version: "1.1.0"
  }
  digests {
    sha256: "v\332,P#q\331\303\200T\337^+$\215\000\332\207\200\236\320X\3636>\256\207\316^$\003\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.cursoradapter"
    artifactId: "cursoradapter"
    version: "1.0.0"
  }
  digests {
    sha256: "\250\034\217\347\210\025\372G\337[t\235\353Rrz\321\037\223\227\332X\261`\027\364\353,\021\342\205d"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.drawerlayout"
    artifactId: "drawerlayout"
    version: "1.1.1"
  }
  digests {
    sha256: ",_\r\3127\216\267\214\242\304@?\230\211\307}\2520Y0\"`\362j\a\376\237c\300\211&\376"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.customview"
    artifactId: "customview"
    version: "1.1.0"
  }
  digests {
    sha256: "\001\367j\260Cw\n\227\260T\004o\230\025q{\202\316\003U\300)g\321la\230\023Y\334\030\232"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2"
    version: "1.3.0"
  }
  digests {
    sha256: "+\3628\030\262:\231m\332\241\265\375[\263!)\332\377k\273-\316\025\026n/\314\335 \020\261\245"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.emoji2"
    artifactId: "emoji2-views-helper"
    version: "1.3.0"
  }
  digests {
    sha256: "\232\023Q)ZOs\235\360\357\3504J\332\251\257\263HV\303\257XMJ\232\373\354\020ZE\271\v"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment"
    version: "1.6.2"
  }
  digests {
    sha256: "\337{\247?7}\275|\315(\261\022\257\320\215(\266\016\302\306\274\221\354e\206\t\310ED*\316\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.loader"
    artifactId: "loader"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\3675\313;U\304X\324p\276\331\342RT7[Q\213K\033\255i&x:p&\333\017P%"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager"
    artifactId: "viewpager"
    version: "1.0.0"
  }
  digests {
    sha256: "\024z\364\341J\031\204\001\r\217\025^^\031\327\201\360<\035p\337\355\002\250\340\321\204(\270\374\206\202"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.fragment"
    artifactId: "fragment-ktx"
    version: "1.6.2"
  }
  digests {
    sha256: "F\f\2206\266M\247\316z\006j\f\261\204\340\024Q\317\304I)\252\033b\376\006\211Y\177#C\370"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.resourceinspection"
    artifactId: "resourceinspection-annotation"
    version: "1.0.1"
  }
  digests {
    sha256: "\214\377\207\016\306\3731\333H\245/Jy#5\264\277\215\340~\003\2757\2021\201Rd3\314\325\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "com.google.android.material"
    artifactId: "material"
    version: "1.12.0"
  }
  digests {
    sha256: "Jg)A\266&\271\253\221\256\211>\322%\230\352S\255i\022\\\205\214\nY\372\233\220\332\245\313\b"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlin"
    artifactId: "kotlin-bom"
    version: "1.8.22"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "com.google.errorprone"
    artifactId: "error_prone_annotations"
    version: "2.15.0"
  }
  digests {
    sha256: "\006pGqCI\347x\232[\333\372\331\321\300\257\237:\036\262\214U\240\356?h\346\202\371\005\304\353"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.cardview"
    artifactId: "cardview"
    version: "1.0.0"
  }
  digests {
    sha256: "\021\223\300L\"\243\326\265\224m\256\237N\214Y\326\255\336jq\266\275]\207\373\231\330-\332\032\376\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.coordinatorlayout"
    artifactId: "coordinatorlayout"
    version: "1.1.0"
  }
  digests {
    sha256: "D\251\343\n\277V\257\020%\305*\n\365\006\376\351\304\023\032\245^\375\245/\237\331E\022\021\305\350\313"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout"
    version: "2.2.1"
  }
  digests {
    sha256: "0\370\327\233x-(:\220\360\266\3676\221i\331\317V\000\221S\177\335`V\321\332\251\363\0037c"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.constraintlayout"
    artifactId: "constraintlayout-core"
    version: "1.1.1"
  }
  digests {
    sha256: "< T2\203(\203\036\266\346\233@\024\366\357\237\252\021\177\324\270\021\222\237:\221\323\3337_,\002"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.dynamicanimation"
    artifactId: "dynamicanimation"
    version: "1.0.0"
  }
  digests {
    sha256: "\316\000Qb\302)\2770\215-[\022\373l\255\bt\006\234\273\352\314\356c\250\031;\320\215@\336\004"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.legacy"
    artifactId: "legacy-support-core-utils"
    version: "1.0.0"
  }
  digests {
    sha256: "\247\355\317\001\325\265+04\a0\'\274Gu\267\212Gd\273b\002\273\221\326\034\202\232\335\215\321\307"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.documentfile"
    artifactId: "documentfile"
    version: "1.0.0"
  }
  digests {
    sha256: "\206Z\006\036\362\372\321e\"\370C56\270\324r\b\304o\367\307tQ\227\337\241\356\264\201\206\224\207"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.localbroadcastmanager"
    artifactId: "localbroadcastmanager"
    version: "1.0.0"
  }
  digests {
    sha256: "\347\0342\214\356\365\304\247\327o-\206\337\033e\326_\342\254\370h\261\244\357\330J?43a\206\330"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.print"
    artifactId: "print"
    version: "1.0.0"
  }
  digests {
    sha256: "\035\\\17715\241\273\246a\3747?\327.\021\353\nJ\333\2639g\207\202m\330\344\031\r]\236\335"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.recyclerview"
    artifactId: "recyclerview"
    version: "1.1.0"
  }
  digests {
    sha256: "\360\322\265\246}\n\221\356\033\034s\357+cj\201\363V9%\335\321Z\035N\034A\354(\336zO"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.transition"
    artifactId: "transition"
    version: "1.5.0"
  }
  digests {
    sha256: "\n\246j\016\244\006\322Z\020\221\371j;u;K\022\344O\334C\271\036\305,\027\203\036\2341\365K"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.viewpager2"
    artifactId: "viewpager2"
    version: "1.0.0"
  }
  digests {
    sha256: "\351\\\0001\324\314$|\324\201\226\306(~X\322\316\345M\234y\270Z\376\247\311\t 3\002u\257"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-fragment"
    version: "2.8.9"
  }
  digests {
    sha256: "T\247\025\314jb\212\243>ZN\361\302\364H\b\263v\305T\020\307\224>\2444\224\320\366n\257\243"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-runtime"
    version: "2.8.9"
  }
  digests {
    sha256: "?$\373\235\250xll^5j\206\327\177c\342\3164s\205\034\374(\3311\361QW\240\303Z\\"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-common"
    version: "2.8.9"
  }
  digests {
    sha256: "5A\372\243\2421\305\203AId\210p\345~3\205\316[\343\033\246Y\267\n\262x1\237e\271\377"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core"
    version: "1.6.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-core-jvm"
    version: "1.6.3"
  }
  digests {
    sha256: ")\310!\250\324\342\\\277\344\362\316\226\315\324Roa\370\364\346\232\023_\226\022\243J\201\331;e\361"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "org.jetbrains.kotlinx"
    artifactId: "kotlinx-serialization-bom"
    version: "1.6.3"
  }
  repo_index {
    value: 1
  }
}
library {
  maven_library {
    groupId: "androidx.navigation"
    artifactId: "navigation-ui"
    version: "2.8.9"
  }
  digests {
    sha256: "up\252\272\037\231\361\"\200\220}\3348v\375^DQjD\315C\305\356\035\262D\227\v\020\2028"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.slidingpanelayout"
    artifactId: "slidingpanelayout"
    version: "1.2.0"
  }
  digests {
    sha256: "_S3\233\342\244\371\n\232\276\243W\035\325\236p\250\244\236\177\025\335\202\227J8\230\264e.\207\024"
  }
  repo_index {
  }
}
library {
  maven_library {
    groupId: "androidx.window"
    artifactId: "window"
    version: "1.0.0"
  }
  digests {
    sha256: "2\022\230[\344\022ss\312M\016\247\370\270\032%\n\342\020^\222Oy@\020]\006z\017\232\3010"
  }
  repo_index {
  }
}
library_dependencies {
  library_dep_index: 1
}
library_dependencies {
  library_index: 1
  library_dep_index: 2
}
library_dependencies {
  library_index: 2
  library_dep_index: 3
}
library_dependencies {
  library_index: 3
  library_dep_index: 4
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 5
  library_dep_index: 3
}
library_dependencies {
  library_index: 6
  library_dep_index: 3
  library_dep_index: 7
}
library_dependencies {
  library_index: 7
  library_dep_index: 3
}
library_dependencies {
  library_index: 8
  library_dep_index: 9
  library_dep_index: 1
  library_dep_index: 42
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 36
  library_dep_index: 45
  library_dep_index: 46
  library_dep_index: 48
  library_dep_index: 49
  library_dep_index: 50
  library_dep_index: 18
  library_dep_index: 33
  library_dep_index: 39
  library_dep_index: 54
  library_dep_index: 37
  library_dep_index: 3
  library_dep_index: 42
}
library_dependencies {
  library_index: 9
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 18
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 37
  library_dep_index: 31
  library_dep_index: 3
  library_dep_index: 41
}
library_dependencies {
  library_index: 10
  library_dep_index: 11
}
library_dependencies {
  library_index: 11
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 12
  library_dep_index: 12
}
library_dependencies {
  library_index: 12
  library_dep_index: 10
  library_dep_index: 10
}
library_dependencies {
  library_index: 13
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 15
  library_dep_index: 17
  library_dep_index: 18
  library_dep_index: 40
  library_dep_index: 3
  library_dep_index: 36
}
library_dependencies {
  library_index: 14
  library_dep_index: 3
}
library_dependencies {
  library_index: 15
  library_dep_index: 1
  library_dep_index: 16
}
library_dependencies {
  library_index: 17
  library_dep_index: 1
}
library_dependencies {
  library_index: 18
  library_dep_index: 1
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 39
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 26
  library_dep_index: 27
}
library_dependencies {
  library_index: 19
  library_dep_index: 1
}
library_dependencies {
  library_index: 20
  library_dep_index: 1
  library_dep_index: 19
}
library_dependencies {
  library_index: 21
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 22
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 18
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 27
}
library_dependencies {
  library_index: 22
  library_dep_index: 23
  library_dep_index: 25
  library_dep_index: 6
}
library_dependencies {
  library_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 24
  library_dep_index: 25
  library_dep_index: 6
  library_dep_index: 5
}
library_dependencies {
  library_index: 25
  library_dep_index: 22
  library_dep_index: 23
  library_dep_index: 24
}
library_dependencies {
  library_index: 26
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 21
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 27
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 18
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
}
library_dependencies {
  library_index: 27
  library_dep_index: 19
  library_dep_index: 20
  library_dep_index: 26
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 18
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
}
library_dependencies {
  library_index: 28
  library_dep_index: 26
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 26
  library_dep_index: 29
  library_dep_index: 18
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 27
}
library_dependencies {
  library_index: 29
  library_dep_index: 1
  library_dep_index: 18
  library_dep_index: 30
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 18
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 27
}
library_dependencies {
  library_index: 30
  library_dep_index: 1
  library_dep_index: 31
}
library_dependencies {
  library_index: 31
  library_dep_index: 1
}
library_dependencies {
  library_index: 32
  library_dep_index: 1
  library_dep_index: 18
  library_dep_index: 3
  library_dep_index: 22
  library_dep_index: 21
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 18
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 27
}
library_dependencies {
  library_index: 33
  library_dep_index: 1
  library_dep_index: 3
  library_dep_index: 21
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 18
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 27
}
library_dependencies {
  library_index: 34
  library_dep_index: 33
  library_dep_index: 3
  library_dep_index: 22
  library_dep_index: 21
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 18
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 27
}
library_dependencies {
  library_index: 35
  library_dep_index: 1
  library_dep_index: 36
  library_dep_index: 26
  library_dep_index: 33
  library_dep_index: 37
  library_dep_index: 3
  library_dep_index: 22
  library_dep_index: 21
  library_dep_index: 26
  library_dep_index: 28
  library_dep_index: 29
  library_dep_index: 18
  library_dep_index: 32
  library_dep_index: 33
  library_dep_index: 34
  library_dep_index: 27
}
library_dependencies {
  library_index: 36
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 3
  library_dep_index: 13
}
library_dependencies {
  library_index: 37
  library_dep_index: 1
  library_dep_index: 19
  library_dep_index: 21
  library_dep_index: 3
  library_dep_index: 38
}
library_dependencies {
  library_index: 38
  library_dep_index: 37
  library_dep_index: 3
  library_dep_index: 37
}
library_dependencies {
  library_index: 39
  library_dep_index: 1
  library_dep_index: 15
  library_dep_index: 30
  library_dep_index: 16
}
library_dependencies {
  library_index: 40
  library_dep_index: 1
  library_dep_index: 10
}
library_dependencies {
  library_index: 41
  library_dep_index: 9
  library_dep_index: 36
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 38
  library_dep_index: 3
  library_dep_index: 9
}
library_dependencies {
  library_index: 42
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 43
  library_dep_index: 44
  library_dep_index: 8
}
library_dependencies {
  library_index: 43
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 10
}
library_dependencies {
  library_index: 44
  library_dep_index: 43
  library_dep_index: 17
  library_dep_index: 10
}
library_dependencies {
  library_index: 45
  library_dep_index: 1
}
library_dependencies {
  library_index: 46
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 47
}
library_dependencies {
  library_index: 47
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 10
}
library_dependencies {
  library_index: 48
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 29
  library_dep_index: 30
  library_dep_index: 49
}
library_dependencies {
  library_index: 49
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 48
  library_dep_index: 48
}
library_dependencies {
  library_index: 50
  library_dep_index: 9
  library_dep_index: 1
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 36
  library_dep_index: 26
  library_dep_index: 18
  library_dep_index: 33
  library_dep_index: 35
  library_dep_index: 51
  library_dep_index: 39
  library_dep_index: 37
  library_dep_index: 52
  library_dep_index: 3
  library_dep_index: 53
}
library_dependencies {
  library_index: 51
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 27
  library_dep_index: 33
}
library_dependencies {
  library_index: 52
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 47
}
library_dependencies {
  library_index: 53
  library_dep_index: 41
  library_dep_index: 12
  library_dep_index: 36
  library_dep_index: 50
  library_dep_index: 28
  library_dep_index: 34
  library_dep_index: 38
  library_dep_index: 3
  library_dep_index: 50
}
library_dependencies {
  library_index: 54
  library_dep_index: 1
}
library_dependencies {
  library_index: 55
  library_dep_index: 56
  library_dep_index: 57
  library_dep_index: 9
  library_dep_index: 1
  library_dep_index: 8
  library_dep_index: 58
  library_dep_index: 59
  library_dep_index: 60
  library_dep_index: 13
  library_dep_index: 46
  library_dep_index: 62
  library_dep_index: 14
  library_dep_index: 50
  library_dep_index: 18
  library_dep_index: 67
  library_dep_index: 54
  library_dep_index: 68
  library_dep_index: 43
  library_dep_index: 69
}
library_dependencies {
  library_index: 56
  library_dep_index: 3
  library_dep_index: 5
  library_dep_index: 6
  library_dep_index: 7
}
library_dependencies {
  library_index: 58
  library_dep_index: 1
}
library_dependencies {
  library_index: 59
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 47
  library_dep_index: 10
}
library_dependencies {
  library_index: 60
  library_dep_index: 8
  library_dep_index: 61
  library_dep_index: 13
  library_dep_index: 39
}
library_dependencies {
  library_index: 61
  library_dep_index: 1
}
library_dependencies {
  library_index: 62
  library_dep_index: 13
  library_dep_index: 10
  library_dep_index: 63
}
library_dependencies {
  library_index: 63
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 64
  library_dep_index: 51
  library_dep_index: 65
  library_dep_index: 66
}
library_dependencies {
  library_index: 64
  library_dep_index: 1
}
library_dependencies {
  library_index: 65
  library_dep_index: 1
}
library_dependencies {
  library_index: 66
  library_dep_index: 1
}
library_dependencies {
  library_index: 67
  library_dep_index: 1
  library_dep_index: 13
  library_dep_index: 47
  library_dep_index: 10
}
library_dependencies {
  library_index: 68
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 13
  library_dep_index: 62
}
library_dependencies {
  library_index: 69
  library_dep_index: 1
  library_dep_index: 50
  library_dep_index: 67
  library_dep_index: 13
  library_dep_index: 10
}
library_dependencies {
  library_index: 70
  library_dep_index: 53
  library_dep_index: 71
  library_dep_index: 77
  library_dep_index: 3
  library_dep_index: 73
  library_dep_index: 71
  library_dep_index: 76
  library_dep_index: 72
}
library_dependencies {
  library_index: 71
  library_dep_index: 41
  library_dep_index: 14
  library_dep_index: 10
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 72
  library_dep_index: 3
  library_dep_index: 73
  library_dep_index: 72
  library_dep_index: 70
  library_dep_index: 76
}
library_dependencies {
  library_index: 72
  library_dep_index: 1
  library_dep_index: 12
  library_dep_index: 36
  library_dep_index: 21
  library_dep_index: 32
  library_dep_index: 34
  library_dep_index: 35
  library_dep_index: 39
  library_dep_index: 38
  library_dep_index: 3
  library_dep_index: 73
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 76
}
library_dependencies {
  library_index: 73
  library_dep_index: 74
}
library_dependencies {
  library_index: 74
  library_dep_index: 3
  library_dep_index: 75
  library_dep_index: 5
}
library_dependencies {
  library_index: 75
  library_dep_index: 74
  library_dep_index: 73
}
library_dependencies {
  library_index: 76
  library_dep_index: 14
  library_dep_index: 47
  library_dep_index: 46
  library_dep_index: 71
  library_dep_index: 68
  library_dep_index: 55
  library_dep_index: 70
  library_dep_index: 71
  library_dep_index: 72
}
library_dependencies {
  library_index: 77
  library_dep_index: 1
  library_dep_index: 47
  library_dep_index: 13
  library_dep_index: 78
  library_dep_index: 68
}
library_dependencies {
  library_index: 78
  library_dep_index: 3
  library_dep_index: 22
  library_dep_index: 1
  library_dep_index: 10
  library_dep_index: 13
}
module_dependencies {
  module_name: "base"
  dependency_index: 0
  dependency_index: 8
  dependency_index: 55
  dependency_index: 60
  dependency_index: 70
  dependency_index: 76
}
repositories {
  maven_repo {
    url: "https://dl.google.com/dl/android/maven2/"
  }
}
repositories {
  maven_repo {
    url: "https://repo.maven.apache.org/maven2/"
  }
}
