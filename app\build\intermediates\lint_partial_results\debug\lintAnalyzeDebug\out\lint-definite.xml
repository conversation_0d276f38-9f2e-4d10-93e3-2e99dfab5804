<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.1" type="incidents">

    <incident
        id="FragmentTagUsage"
        severity="warning"
        message="Replace the &lt;fragment> tag with FragmentContainerView.">
        <fix-replace
            description="Replace with androidx.fragment.app.FragmentContainerView"
            oldString="fragment"
            replacement="androidx.fragment.app.FragmentContainerView"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="8"
            column="6"
            startOffset="358"
            endLine="8"
            endColumn="14"
            endOffset="366"/>
    </incident>

    <incident
        id="VectorRaster"
        severity="warning"
        message="Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/fubuki_header.xml"
            line="4"
            column="20"
            startOffset="174"
            endLine="4"
            endColumn="25"
            endOffset="179"/>
    </incident>

    <incident
        id="AndroidGradlePluginVersion"
        severity="warning"
        message="A newer version of com.android.application than 8.9.1 is available: 8.10.1. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.1 is difficult: 8.9.3)">
        <fix-alternatives>
            <fix-replace
                description="Change to 8.10.1"
                family="Update versions"
                oldString="8.9.1"
                replacement="8.10.1"
                priority="0"/>
            <fix-replace
                description="Change to 8.9.3"
                family="Update versions"
                robot="true"
                independent="true"
                oldString="8.9.1"
                replacement="8.9.3"
                priority="0"/>
        </fix-alternatives>
        <location
            file="../gradle/libs.versions.toml"
            line="2"
            column="7"
            startOffset="17"
            endLine="2"
            endColumn="14"
            endOffset="24"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.7.0"
            replacement="1.7.1"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="6"
            column="13"
            startOffset="100"
            endLine="6"
            endColumn="20"
            endOffset="107"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-fragment than 2.8.9 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.8.9"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="9"
            column="22"
            startOffset="176"
            endLine="9"
            endColumn="29"
            endOffset="183"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.navigation:navigation-ui than 2.8.9 is available: 2.9.0">
        <fix-replace
            description="Change to 2.9.0"
            family="Update versions"
            oldString="2.8.9"
            replacement="2.9.0"
            priority="0"/>
        <location
            file="../gradle/libs.versions.toml"
            line="10"
            column="16"
            startOffset="199"
            endLine="10"
            endColumn="23"
            endOffset="206"/>
    </incident>

    <incident
        id="Typos"
        severity="warning"
        message="&quot;populer&quot; is a common misspelling; did you mean &quot;popular&quot;?">
        <fix-replace
            description="Replace with &quot;popular&quot;"
            oldString="populer"
            replacement="popular"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="3"
            column="107"
            startOffset="172"
            endLine="3"
            endColumn="107"
            endOffset="179"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="This folder configuration (`v23`) is unnecessary; `minSdkVersion` is 24. Merge all the resources in this folder into `values`.">
        <fix-data file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-v23" folderName="values" requiresApi="24"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values-v23"/>
    </incident>

    <incident
        id="TypographyEllipsis"
        severity="warning"
        message="Replace &quot;...&quot; with ellipsis character (…, &amp;#8230;) ?">
        <fix-replace
            description="Replace with …"
            oldString="..."
            replacement="…"
            priority="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="3"
            column="32"
            startOffset="97"
            endLine="3"
            endColumn="185"
            endOffset="250"/>
    </incident>

    <incident
        id="IconLocation"
        severity="warning"
        message="Found bitmap drawable `res/drawable/header_image.jpg` in densityless folder">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/header_image.jpg"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="25"
            column="14"
            startOffset="1015"
            endLine="25"
            endColumn="23"
            endOffset="1024"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="66"
            column="22"
            startOffset="2702"
            endLine="66"
            endColumn="31"
            endOffset="2711"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="96"
            column="6"
            startOffset="4066"
            endLine="96"
            endColumn="75"
            endOffset="4135"/>
    </incident>

</incidents>
