D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\layout\content_main.xml:8: Warning: Replace the <fragment> tag with FragmentContainerView. [FragmentTagUsage from androidx.fragment]
    <fragment
     ~~~~~~~~

   Explanation for issues of type "FragmentTagUsage":
   FragmentContainerView replaces the <fragment> tag as the preferred         
          way of adding fragments via XML. Unlike the <fragment> tag,
   FragmentContainerView                 uses a normal FragmentTransaction
   under the hood to add the initial fragment,                 allowing
   further FragmentTransaction operations on the FragmentContainerView        
           and providing a consistent timing for lifecycle events.

   https://developer.android.com/reference/androidx/fragment/app/FragmentContainerView.html

   Vendor: Android Open Source Project
   Identifier: androidx.fragment
   Feedback: https://issuetracker.google.com/issues/new?component=460964

D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\drawable\fubuki_header.xml:4: Warning: Limit vector icons sizes to 200×200 to keep icon drawing fast; see https://developer.android.com/studio/write/vector-asset-studio#when for more [VectorRaster]
    android:width="400dp"
                   ~~~~~

   Explanation for issues of type "VectorRaster":
   Vector icons require API 21 or API 24 depending on used features, but when
   minSdkVersion is less than 21 or 24 and Android Gradle plugin 1.4 or higher
   is used, a vector drawable placed in the drawable folder is automatically
   moved to drawable-anydpi-v21 or drawable-anydpi-v24 and bitmap images are
   generated for different screen resolutions for backwards compatibility.

   However, there are some limitations to this raster image generation, and
   this lint check flags elements and attributes that are not fully supported.
   You should manually check whether the generated output is acceptable for
   those older devices.

D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.1 is available: 8.10.1. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.1 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.1"
      ~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.1 is available: 8.10.1. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.1 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.1"
      ~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\gradle\libs.versions.toml:2: Warning: A newer version of com.android.application than 8.9.1 is available: 8.10.1. (There is also a newer version of 8.9.𝑥 available, if upgrading to 8.10.1 is difficult: 8.9.3) [AndroidGradlePluginVersion]
agp = "8.9.1"
      ~~~~~~~

   Explanation for issues of type "AndroidGradlePluginVersion":
   This detector looks for usage of the Android Gradle Plugin where the
   version you are using is not the current stable release. Using older
   versions is fine, and there are cases where you deliberately want to stick
   with an older version. However, you may simply not be aware that a more
   recent version is available, and that is what this lint check helps find.

D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\gradle\libs.versions.toml:6: Warning: A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1 [GradleDependency]
appcompat = "1.7.0"
            ~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\gradle\libs.versions.toml:6: Warning: A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1 [GradleDependency]
appcompat = "1.7.0"
            ~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\gradle\libs.versions.toml:6: Warning: A newer version of androidx.appcompat:appcompat than 1.7.0 is available: 1.7.1 [GradleDependency]
appcompat = "1.7.0"
            ~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\gradle\libs.versions.toml:9: Warning: A newer version of androidx.navigation:navigation-fragment than 2.8.9 is available: 2.9.0 [GradleDependency]
navigationFragment = "2.8.9"
                     ~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\gradle\libs.versions.toml:9: Warning: A newer version of androidx.navigation:navigation-fragment than 2.8.9 is available: 2.9.0 [GradleDependency]
navigationFragment = "2.8.9"
                     ~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\gradle\libs.versions.toml:9: Warning: A newer version of androidx.navigation:navigation-fragment than 2.8.9 is available: 2.9.0 [GradleDependency]
navigationFragment = "2.8.9"
                     ~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\gradle\libs.versions.toml:10: Warning: A newer version of androidx.navigation:navigation-ui than 2.8.9 is available: 2.9.0 [GradleDependency]
navigationUi = "2.8.9"
               ~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\gradle\libs.versions.toml:10: Warning: A newer version of androidx.navigation:navigation-ui than 2.8.9 is available: 2.9.0 [GradleDependency]
navigationUi = "2.8.9"
               ~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\gradle\libs.versions.toml:10: Warning: A newer version of androidx.navigation:navigation-ui than 2.8.9 is available: 2.9.0 [GradleDependency]
navigationUi = "2.8.9"
               ~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values\strings.xml:3: Warning: "populer" is a common misspelling; did you mean "popular"? [Typos]
    <string name="review_text">Shirakami Fubuki adalah seorang Virtual YouTuber dari Hololive yang sangat populer. Dia dikenal dengan kepribadiannya yang ceria dan suka bermain game...</string>
                                                                                                          ^

   Explanation for issues of type "Typos":
   This check looks through the string definitions, and if it finds any words
   that look like likely misspellings, they are flagged.

D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values-v23: Warning: This folder configuration (v23) is unnecessary; minSdkVersion is 24. Merge all the resources in this folder into values. [ObsoleteSdkInt]

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values\colors.xml:3: Warning: The resource R.color.black appears to be unused [UnusedResources]
    <color name="black">#FF000000</color>
           ~~~~~~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values\colors.xml:4: Warning: The resource R.color.green appears to be unused [UnusedResources]
    <color name="green">#96ff84</color>
           ~~~~~~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\layout\content_main.xml:2: Warning: The resource R.layout.content_main appears to be unused [UnusedResources]
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values\dimens.xml:2: Warning: The resource R.dimen.fab_margin appears to be unused [UnusedResources]
    <dimen name="fab_margin">16dp</dimen>
           ~~~~~~~~~~~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\drawable\header_image.jpg: Warning: The resource R.drawable.header_image appears to be unused [UnusedResources]
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\drawable\ic_review.xml:1: Warning: The resource R.drawable.ic_review appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="24dp" android:tint="#000000" android:viewportHeight="24" android:viewportWidth="24" android:width="24dp">
^
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\menu\menu_main.xml:1: Warning: The resource R.menu.menu_main appears to be unused [UnusedResources]
<menu xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\navigation\nav_graph.xml:2: Warning: The resource R.navigation.nav_graph appears to be unused [UnusedResources]
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
^
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values\strings.xml:3: Warning: The resource R.string.review_text appears to be unused [UnusedResources]
    <string name="review_text">Shirakami Fubuki adalah seorang Virtual YouTuber dari Hololive yang sangat populer. Dia dikenal dengan kepribadiannya yang ceria dan suka bermain game...</string>
            ~~~~~~~~~~~~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values\strings.xml:8: Warning: The resource R.string.first_fragment_label appears to be unused [UnusedResources]
    <string name="first_fragment_label">Profil Fubuki</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values\strings.xml:9: Warning: The resource R.string.second_fragment_label appears to be unused [UnusedResources]
    <string name="second_fragment_label">Informasi Tambahan</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values\strings.xml:12: Warning: The resource R.string.action_settings appears to be unused [UnusedResources]
    <string name="action_settings">Pengaturan</string>
            ~~~~~~~~~~~~~~~~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values\strings.xml:21: Warning: The resource R.string.fubuki_characteristics appears to be unused [UnusedResources]
    <string name="fubuki_characteristics">Karakteristik: Rubah putih yang energik, suka bermain game, memiliki tawa yang khas, dan sangat ramah kepada penonton. Dia juga dikenal sebagai "Friend" oleh para penggemar.</string>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\values\strings.xml:3: Warning: Replace "..." with ellipsis character (…, &#8230;) ? [TypographyEllipsis]
    <string name="review_text">Shirakami Fubuki adalah seorang Virtual YouTuber dari Hololive yang sangat populer. Dia dikenal dengan kepribadiannya yang ceria dan suka bermain game...</string>
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "TypographyEllipsis":
   You can replace the string "..." with a dedicated ellipsis character,
   ellipsis character (u2026, &#8230;). This can help make the text more
   readable.

   https://en.wikipedia.org/wiki/Ellipsis

D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\drawable\header_image.jpg: Warning: Found bitmap drawable res/drawable/header_image.jpg in densityless folder [IconLocation]

   Explanation for issues of type "IconLocation":
   The res/drawable folder is intended for density-independent graphics such
   as shapes defined in XML. For bitmaps, move it to drawable-mdpi and
   consider providing higher and lower resolution versions in drawable-ldpi,
   drawable-hdpi and drawable-xhdpi. If the icon really is density independent
   (for example a solid color) you can place it in drawable-nodpi.

   https://developer.android.com/guide/practices/screens_support.html

D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\layout\activity_main.xml:25: Warning: Missing contentDescription attribute on image [ContentDescription]
            <ImageView
             ~~~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\layout\activity_main.xml:66: Warning: Missing contentDescription attribute on image [ContentDescription]
                    <ImageView
                     ~~~~~~~~~
D:\Documents\3School Bullshit\PBP\ModulCoordinatorLayout\app\src\main\res\layout\activity_main.xml:96: Warning: Missing contentDescription attribute on image [ContentDescription]
    <com.google.android.material.floatingactionbutton.FloatingActionButton
     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

0 errors, 34 warnings
