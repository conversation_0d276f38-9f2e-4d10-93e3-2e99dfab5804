<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.tugas.platform.modulcoordinatorlayout" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_main_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="103" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="11" startOffset="4" endLine="38" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="31" startOffset="12" endLine="36" endOffset="46"/></Target><Target id="@+id/fab" view="com.google.android.material.floatingactionbutton.FloatingActionButton"><Expressions/><location startLine="95" startOffset="4" endLine="101" endOffset="59"/></Target></Targets></Layout>