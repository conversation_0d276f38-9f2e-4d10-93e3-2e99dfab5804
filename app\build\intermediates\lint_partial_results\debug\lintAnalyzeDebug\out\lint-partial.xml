<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.9.1" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.tugas.platform.modulcoordinatorlayout.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.black"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="3"
            column="12"
            startOffset="62"
            endLine="3"
            endColumn="24"
            endOffset="74"/>
        <location id="R.color.green"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="4"
            column="12"
            startOffset="104"
            endLine="4"
            endColumn="24"
            endOffset="116"/>
        <location id="R.dimen.fab_margin"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/dimens.xml"
            line="2"
            column="12"
            startOffset="23"
            endLine="2"
            endColumn="29"
            endOffset="40"/>
        <location id="R.drawable.header_image"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/header_image.jpg"/>
        <location id="R.drawable.ic_review"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_review.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="5"
            endColumn="10"
            endOffset="443"/>
        <location id="R.layout.content_main"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/content_main.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="19"
            endColumn="53"
            endOffset="907"/>
        <location id="R.menu.menu_main"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/menu/menu_main.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="10"
            endColumn="8"
            endOffset="429"/>
        <location id="R.navigation.nav_graph"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/navigation/nav_graph.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="28"
            endColumn="14"
            endOffset="1086"/>
        <location id="R.string.action_settings"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="12"
            column="13"
            startOffset="557"
            endLine="12"
            endColumn="35"
            endOffset="579"/>
        <location id="R.string.first_fragment_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="8"
            column="13"
            startOffset="398"
            endLine="8"
            endColumn="40"
            endOffset="425"/>
        <location id="R.string.fubuki_characteristics"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="21"
            column="13"
            startOffset="1486"
            endLine="21"
            endColumn="42"
            endOffset="1515"/>
        <location id="R.string.review_text"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="3"
            column="13"
            startOffset="78"
            endLine="3"
            endColumn="31"
            endOffset="96"/>
        <location id="R.string.second_fragment_label"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/strings.xml"
            line="9"
            column="13"
            startOffset="461"
            endLine="9"
            endColumn="41"
            endOffset="489"/>
        <entry
            name="model"
            string="attr[colorPrimary(R),actionBarSize(R),isLightTheme(R)],color[black(D),green(D)],dimen[fab_margin(D)],drawable[fubuki_header(U),fubuki_header_1(R),header_image(D),ic_fubuki(U),ic_launcher_background(U),ic_launcher_foreground(U),ic_launcher_foreground_1(R),ic_review(D)],id[appBarLayout(D),toolbar(U),fab(D),nav_host_fragment_content_main(U),button_first(U),textview_first(U),button_second(U),textview_second(U),action_settings(D),nav_graph(D),FirstFragment(U),action_FirstFragment_to_SecondFragment(R),SecondFragment(U),action_SecondFragment_to_FirstFragment(R)],layout[activity_main(U),content_main(D),fragment_first(U),fragment_second(U)],menu[menu_main(D)],mipmap[ic_launcher(U),ic_launcher_round(U)],navigation[nav_graph(D)],string[app_name(U),fubuki_title(U),appbar_scrolling_view_behavior(R),fubuki_debut_date(U),fubuki_description(U),next(U),lorem_ipsum(U),previous(U),action_settings(D),first_fragment_label(D),second_fragment_label(D),review_text(D),fubuki_characteristics(D)],style[Theme_ModulCoordinatorLayout(U),TextAppearance_AppCompat_Large(R),TextAppearance_AppCompat_Small(R),Base_Theme_ModulCoordinatorLayout(U),Theme_Material3_DayNight_NoActionBar(R)],xml[data_extraction_rules(U),backup_rules(U)];6^7,b^c,1c^0^25^6^1^24^26^9^32^27^33^28,1d^26^23,1e^29^13^2a^12,1f^2b^15^2a^14,20^2c,21^a^b,22^a^b,23^18^2d^1e^1a^2e^1f,31^34^2,34^35;;;"/>
    </map>

</incidents>
